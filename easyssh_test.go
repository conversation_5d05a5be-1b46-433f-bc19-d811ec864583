package easyssh

import (
	"context"
	"os"
	"os/user"
	"path"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/ssh"
)

func getHostPublicKeyFile(keypath string) (ssh.PublicKey, error) {
	var pubkey ssh.PublicKey
	var err error
	buf, err := os.ReadFile(keypath)
	if err != nil {
		return nil, err
	}

	pubkey, _, _, _, err = ssh.ParseAuthorizedKey(buf)
	if err != nil {
		return nil, err
	}

	return pubkey, nil
}

func TestGetKeyFile(t *testing.T) {
	// missing file
	_, err := getKeyFile("abc", "")
	assert.Error(t, err)
	assert.Equal(t, "open abc: no such file or directory", err.Error())

	// wrong format
	_, err = getKeyFile("./tests/.ssh/id_rsa.pub", "")
	assert.Error(t, err)
	assert.Equal(t, "ssh: no key found", err.<PERSON>rror())

	_, err = getKeyFile("./tests/.ssh/id_rsa", "")
	assert.NoError(t, err)

	_, err = getKeyFile("./tests/.ssh/test", "1234")
	assert.NoError(t, err)
}

func TestRunCommandWithFingerprint(t *testing.T) {
	// wrong fingerprint
	sshConf := &MakeConfig{
		Server:      "localhost",
		User:        "drone-scp",
		Port:        "22",
		KeyPath:     "./tests/.ssh/id_rsa",
		Fingerprint: "wrong",
	}

	outStr, errStr, isTimeout, err := sshConf.Run("whoami", 10)
	assert.Equal(t, "", outStr)
	assert.Equal(t, "", errStr)
	assert.False(t, isTimeout)
	assert.Error(t, err)

	hostKey, err := getHostPublicKeyFile("/etc/ssh/ssh_host_rsa_key.pub")
	assert.NoError(t, err)

	sshConf = &MakeConfig{
		Server:      "localhost",
		User:        "drone-scp",
		Port:        "22",
		KeyPath:     "./tests/.ssh/id_rsa",
		Fingerprint: ssh.FingerprintSHA256(hostKey),
	}

	outStr, errStr, isTimeout, err = sshConf.Run("whoami")
	assert.Equal(t, "drone-scp\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)
}

func TestPrivateKeyAndPassword(t *testing.T) {
	// provide password and ssh private key
	ssh := &MakeConfig{
		Server:   "localhost",
		User:     "drone-scp",
		Port:     "22",
		Password: "1234",
		KeyPath:  "./tests/.ssh/id_rsa",
	}

	outStr, errStr, isTimeout, err := ssh.Run("whoami")
	assert.Equal(t, "drone-scp\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)

	// provide correct password and wrong private key
	ssh = &MakeConfig{
		Server:   "localhost",
		User:     "drone-scp",
		Port:     "22",
		Password: "1234",
		KeyPath:  "./tests/.ssh/id_rsa.pub",
	}

	outStr, errStr, isTimeout, err = ssh.Run("whoami")
	assert.Equal(t, "drone-scp\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)

	// provide wrong password and correct private key
	ssh = &MakeConfig{
		Server:   "localhost",
		User:     "drone-scp",
		Port:     "22",
		Password: "123456",
		KeyPath:  "./tests/.ssh/id_rsa",
	}

	outStr, errStr, isTimeout, err = ssh.Run("whoami")
	assert.Equal(t, "drone-scp\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)
}

func TestRunCommand(t *testing.T) {
	// wrong key
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "drone-scp",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa.pub",
	}

	outStr, errStr, isTimeout, err := ssh.Run("whoami", 10)
	assert.Equal(t, "", outStr)
	assert.Equal(t, "", errStr)
	assert.False(t, isTimeout)
	assert.Error(t, err)

	ssh = &MakeConfig{
		Server:  "localhost",
		User:    "drone-scp",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	outStr, errStr, isTimeout, err = ssh.Run("whoami")
	assert.Equal(t, "drone-scp\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)

	// error message: not found
	outStr, errStr, isTimeout, err = ssh.Run("whoami1234")
	assert.Equal(t, "", outStr)
	assert.Equal(t, "sh: whoami1234: not found\n", errStr)
	assert.True(t, isTimeout)
	// Process exited with status 127
	assert.Error(t, err)

	// error message: Run Command Timeout
	outStr, errStr, isTimeout, err = ssh.Run("sleep 2", 1*time.Second)
	assert.Equal(t, "", outStr)
	assert.Equal(t, "", errStr)
	assert.False(t, isTimeout)
	assert.Error(t, err)
	assert.Equal(t, "Run Command Timeout: "+context.DeadlineExceeded.Error(), err.Error())

	// test exit code
	outStr, errStr, isTimeout, err = ssh.Run("exit 1")
	assert.Equal(t, "", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	// Process exited with status 1
	assert.Error(t, err)
}

func TestSCPCommand(t *testing.T) {
	// wrong key
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "drone-scp",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa.pub",
	}

	err := ssh.Scp("./tests/a.txt", "a.txt")
	assert.Error(t, err)

	ssh = &MakeConfig{
		Server:  "localhost",
		User:    "drone-scp",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	err = ssh.Scp("./tests/a.txt", "a.txt")
	assert.NoError(t, err)

	u, err := user.Lookup("drone-scp")
	if err != nil {
		t.Fatalf("Lookup: %v", err)
	}

	// check file exist
	if _, err := os.Stat(path.Join(u.HomeDir, "a.txt")); os.IsNotExist(err) {
		t.Fatalf("SCP-error: %v", err)
	}
}

func TestSCPCommandWithKey(t *testing.T) {
	ssh := &MakeConfig{
		Server: "localhost",
		User:   "drone-scp",
		Port:   "22",
		Key: `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
	}

	// source file not found
	err := ssh.Scp("./tests/test.txt", "a.txt")
	assert.Error(t, err)

	// target file not found ex: appleboy folder not found
	err = ssh.Scp("./tests/a.txt", "/appleboy/a.txt")
	assert.Error(t, err)

	err = ssh.Scp("./tests/a.txt", "a.txt")
	assert.NoError(t, err)

	u, err := user.Lookup("drone-scp")
	if err != nil {
		t.Fatalf("Lookup: %v", err)
	}

	// check file exist
	if _, err := os.Stat(path.Join(u.HomeDir, "a.txt")); os.IsNotExist(err) {
		t.Fatalf("SCP-error: %v", err)
	}
}

func TestProxyClient(t *testing.T) {
	ssh := &MakeConfig{
		Server:   "localhost",
		User:     "drone-scp",
		Port:     "22",
		Password: "1234",
		Proxy: DefaultConfig{
			User:     "drone-scp",
			Server:   "localhost",
			Port:     "22",
			Password: "123456",
		},
	}

	// password of proxy client is incorrect.
	// can't connect proxy server
	session, client, err := ssh.Connect()
	assert.Nil(t, session)
	assert.Nil(t, client)
	assert.Error(t, err)

	ssh = &MakeConfig{
		Server:   "www.che.ccu.edu.tw",
		User:     "drone-scp",
		Port:     "228",
		Password: "123456",
		Proxy: DefaultConfig{
			User:    "drone-scp",
			Server:  "localhost",
			Port:    "22",
			KeyPath: "./tests/.ssh/id_rsa",
		},
	}

	// proxy client can't dial to target server
	session, client, err = ssh.Connect()
	assert.Nil(t, session)
	assert.Nil(t, client)
	assert.Error(t, err)

	ssh = &MakeConfig{
		Server:   "localhost",
		User:     "drone-scp",
		Port:     "22",
		Password: "123456",
		Proxy: DefaultConfig{
			User:    "drone-scp",
			Server:  "localhost",
			Port:    "22",
			KeyPath: "./tests/.ssh/id_rsa",
		},
	}

	// proxy client can't create new client connection of target
	session, client, err = ssh.Connect()
	assert.Nil(t, session)
	assert.Nil(t, client)
	assert.Error(t, err)

	ssh = &MakeConfig{
		User:    "drone-scp",
		Server:  "localhost",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
		Proxy: DefaultConfig{
			User:    "drone-scp",
			Server:  "localhost",
			Port:    "22",
			KeyPath: "./tests/.ssh/id_rsa",
		},
	}

	session, client, err = ssh.Connect()
	assert.NotNil(t, session)
	assert.NotNil(t, client)
	assert.NoError(t, err)
}

func TestProxyClientSSHCommand(t *testing.T) {
	ssh := &MakeConfig{
		User:    "drone-scp",
		Server:  "localhost",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
		Proxy: DefaultConfig{
			User:    "drone-scp",
			Server:  "localhost",
			Port:    "22",
			KeyPath: "./tests/.ssh/id_rsa",
		},
	}

	outStr, errStr, isTimeout, err := ssh.Run("whoami")
	assert.Equal(t, "drone-scp\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)
}

func TestSCPCommandWithPassword(t *testing.T) {
	ssh := &MakeConfig{
		Server:   "localhost",
		User:     "drone-scp",
		Port:     "22",
		Password: "1234",
		Timeout:  60 * time.Second,
	}

	err := ssh.Scp("./tests/b.txt", "b.txt")
	assert.NoError(t, err)

	u, err := user.Lookup("drone-scp")
	if err != nil {
		t.Fatalf("Lookup: %v", err)
	}

	// check file exist
	if _, err := os.Stat(path.Join(u.HomeDir, "b.txt")); os.IsNotExist(err) {
		t.Fatalf("SCP-error: %v", err)
	}
}

func TestWrongRawKey(t *testing.T) {
	// wrong key
	ssh := &MakeConfig{
		Server: "localhost",
		User:   "drone-scp",
		Port:   "22",
		Key:    "appleboy",
	}

	outStr, errStr, isTimeout, err := ssh.Run("whoami")
	assert.Equal(t, "", outStr)
	assert.Equal(t, "", errStr)
	assert.False(t, isTimeout)
	assert.Error(t, err)
}

func TestExitCode(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "drone-scp",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
		Timeout: 60 * time.Second,
	}

	outStr, errStr, isTimeout, err := ssh.Run("set -e;echo 1; mkdir a;mkdir a;echo 2")
	assert.Equal(t, "1\n", outStr)
	assert.Equal(t, "mkdir: can't create directory 'a': File exists\n", errStr)
	assert.True(t, isTimeout)
	assert.Error(t, err)
}

func TestSSHWithPassphrase(t *testing.T) {
	ssh := &MakeConfig{
		Server:     "localhost",
		User:       "drone-scp",
		Port:       "22",
		KeyPath:    "./tests/.ssh/test",
		Passphrase: "1234",
		Timeout:    60 * time.Second,
	}

	outStr, errStr, isTimeout, err := ssh.Run("set -e;echo 1; mkdir test1234;mkdir test1234;echo 2")
	assert.Equal(t, "1\n", outStr)
	assert.Equal(t, "mkdir: can't create directory 'test1234': File exists\n", errStr)
	assert.True(t, isTimeout)
	assert.Error(t, err)
}

func TestSCPCommandUseInsecureCipher(t *testing.T) {
	ssh := &MakeConfig{
		Server:            "localhost",
		User:              "drone-scp",
		Port:              "22",
		KeyPath:           "./tests/.ssh/id_rsa",
		UseInsecureCipher: true,
	}

	err := ssh.Scp("./tests/a.txt", "a.txt")
	assert.NoError(t, err)

	u, err := user.Lookup("drone-scp")
	if err != nil {
		t.Fatalf("Lookup: %v", err)
	}

	// check file exist
	if _, err := os.Stat(path.Join(u.HomeDir, "a.txt")); os.IsNotExist(err) {
		t.Fatalf("SCP-error: %v", err)
	}
}

// TestRootAccount test root account
func TestRootAccount(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "root",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	outStr, errStr, isTimeout, err := ssh.Run("whoami")
	assert.Equal(t, "root\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)
}

// TestSudoCommand
func TestSudoCommand(t *testing.T) {
	ssh := &MakeConfig{
		Server:     "localhost",
		User:       "drone-scp",
		Port:       "22",
		KeyPath:    "./tests/.ssh/id_rsa",
		RequestPty: true,
	}

	outStr, errStr, isTimeout, err := ssh.Run(`sudo su - -c "whoami"`)
	assert.Equal(t, "root\r\n", outStr)
	assert.Equal(t, "", errStr)
	assert.True(t, isTimeout)
	assert.NoError(t, err)
}

func TestCommandTimeout(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "root",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	outStr, errStr, isTimeout, err := ssh.Run("whoami; sleep 2", 1*time.Second)
	assert.Equal(t, "root\n", outStr)
	assert.Equal(t, "", errStr)
	assert.False(t, isTimeout)
	assert.NotNil(t, err)
	assert.Equal(t, "Run Command Timeout: "+context.DeadlineExceeded.Error(), err.Error())
}

func TestSftpClient(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "root",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	sftpClient, client, err := ssh.SftpClient()
	if err != nil {
		t.Skipf("SFTP connection failed (this is expected if SSH server doesn't support SFTP): %v", err)
		return
	}
	defer client.Close()
	defer sftpClient.Close()

	assert.NotNil(t, sftpClient)
	assert.NotNil(t, client)
}

func TestSftpUploadDownload(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "root",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	// Create a test file
	testContent := "Hello SFTP World!"
	localFile := "./tests/sftp_test_upload.txt"
	remoteFile := "/tmp/sftp_test_remote.txt"
	downloadFile := "./tests/sftp_test_download.txt"

	// Create local test file
	err := os.WriteFile(localFile, []byte(testContent), 0o644)
	assert.NoError(t, err)
	defer os.Remove(localFile)
	defer os.Remove(downloadFile)

	// Test upload
	err = ssh.SftpUpload(localFile, remoteFile)
	if err != nil {
		t.Skipf("SFTP upload failed (this is expected if SSH server doesn't support SFTP): %v", err)
		return
	}

	// Test download
	err = ssh.SftpDownload(remoteFile, downloadFile)
	assert.NoError(t, err)

	// Verify content
	downloadedContent, err := os.ReadFile(downloadFile)
	assert.NoError(t, err)
	assert.Equal(t, testContent, string(downloadedContent))

	// Cleanup remote file
	err = ssh.SftpRemove(remoteFile)
	assert.NoError(t, err)
}

func TestSftpDirectoryOperations(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "root",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	testDir := "/tmp/sftp_test_dir"
	nestedDir := "/tmp/sftp_test_nested/sub1/sub2"

	// Test create directory
	err := ssh.SftpMkdir(testDir)
	if err != nil {
		t.Skipf("SFTP mkdir failed (this is expected if SSH server doesn't support SFTP): %v", err)
		return
	}

	// Test create nested directories
	err = ssh.SftpMkdirAll(nestedDir)
	assert.NoError(t, err)

	// Test list directory (should contain our test directory)
	fileInfos, err := ssh.SftpList("/tmp")
	assert.NoError(t, err)
	assert.NotEmpty(t, fileInfos)

	// Check if our test directory exists in the list
	found := false
	for _, info := range fileInfos {
		if info.Name() == "sftp_test_dir" && info.IsDir() {
			found = true
			break
		}
	}
	assert.True(t, found, "Test directory should be found in listing")

	// Test stat on directory
	dirInfo, err := ssh.SftpStat(testDir)
	assert.NoError(t, err)
	assert.True(t, dirInfo.IsDir())
	assert.Equal(t, "sftp_test_dir", dirInfo.Name())

	// Cleanup
	err = ssh.SftpRemove(testDir)
	assert.NoError(t, err)

	// Cleanup nested directories (remove from deepest to shallowest)
	err = ssh.SftpRemove(nestedDir)
	assert.NoError(t, err)
	err = ssh.SftpRemove("/tmp/sftp_test_nested/sub1")
	assert.NoError(t, err)
	err = ssh.SftpRemove("/tmp/sftp_test_nested")
	assert.NoError(t, err)
}

func TestSftpFileOperations(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "root",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	// Create a test file for operations
	testContent := "SFTP file operations test"
	localFile := "./tests/sftp_ops_test.txt"
	remoteFile := "/tmp/sftp_ops_test.txt"

	// Create local test file
	err := os.WriteFile(localFile, []byte(testContent), 0o644)
	assert.NoError(t, err)
	defer os.Remove(localFile)

	// Upload file
	err = ssh.SftpUpload(localFile, remoteFile)
	if err != nil {
		t.Skipf("SFTP upload failed (this is expected if SSH server doesn't support SFTP): %v", err)
		return
	}

	// Test file stat
	fileInfo, err := ssh.SftpStat(remoteFile)
	assert.NoError(t, err)
	assert.False(t, fileInfo.IsDir())
	assert.Equal(t, "sftp_ops_test.txt", fileInfo.Name())
	assert.Equal(t, int64(len(testContent)), fileInfo.Size())

	// Test chmod
	err = ssh.SftpChmod(remoteFile, 0o755)
	assert.NoError(t, err)

	// Verify permissions changed (note: exact permission checking may vary by system)
	fileInfo, err = ssh.SftpStat(remoteFile)
	assert.NoError(t, err)
	// The mode should include the new permissions
	assert.NotEqual(t, os.FileMode(0o644), fileInfo.Mode().Perm())

	// Cleanup
	err = ssh.SftpRemove(remoteFile)
	assert.NoError(t, err)
}

func TestSftpRemoveAll(t *testing.T) {
	ssh := &MakeConfig{
		Server:  "localhost",
		User:    "root",
		Port:    "22",
		KeyPath: "./tests/.ssh/id_rsa",
	}

	// Create test directory structure
	testDir := "/tmp/sftp_removeall_test"
	subDir := testDir + "/subdir"
	testFile1 := testDir + "/file1.txt"
	testFile2 := subDir + "/file2.txt"

	// Create directories
	err := ssh.SftpMkdirAll(subDir)
	if err != nil {
		t.Skipf("SFTP mkdir failed (this is expected if SSH server doesn't support SFTP): %v", err)
		return
	}

	// Create test files
	testContent := "test content for removeall"
	localFile := "./tests/sftp_removeall_temp.txt"
	err = os.WriteFile(localFile, []byte(testContent), 0644)
	assert.NoError(t, err)
	defer os.Remove(localFile)

	// Upload files to test directory structure
	err = ssh.SftpUpload(localFile, testFile1)
	assert.NoError(t, err)
	err = ssh.SftpUpload(localFile, testFile2)
	assert.NoError(t, err)

	// Verify structure exists
	fileInfo, err := ssh.SftpStat(testDir)
	assert.NoError(t, err)
	assert.True(t, fileInfo.IsDir())

	// Test RemoveAll - should remove entire directory tree
	err = ssh.SftpRemoveAll(testDir)
	assert.NoError(t, err)

	// Verify directory no longer exists
	_, err = ssh.SftpStat(testDir)
	assert.Error(t, err) // Should fail because directory was removed
}
