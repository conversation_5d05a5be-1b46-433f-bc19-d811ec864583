module example

go 1.23.0

require github.com/MrHIDEn/easyssh-proxy v0.0.0

require (
	github.com/ScaleFT/sshkeys v1.4.0 // indirect
	github.com/dchest/bcrypt_pbkdf v0.0.0-20150205184540-83f37f9c154a // indirect
	github.com/kr/fs v0.1.0 // indirect
	github.com/pkg/sftp v1.13.9 // indirect
	golang.org/x/crypto v0.41.0 // indirect
	golang.org/x/sys v0.35.0 // indirect
)

replace github.com/MrHIDEn/easyssh-proxy => ../../
