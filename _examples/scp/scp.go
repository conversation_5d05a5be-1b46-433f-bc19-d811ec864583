package main

import (
	"fmt"

	"github.com/MrHIDEn/easyssh-proxy"
)

func main() {
	// Create MakeConfig instance with remote username, server address and path to private key.
	ssh := &easyssh.MakeConfig{
		User:     "easyssh",
		Server:   "example.com",
		Password: "123qwe",
		Port:     "22",
	}

	// Call Scp method with file you want to upload to remote server.
	// Please make sure the `tmp` folder exists.
	err := ssh.Scp("/root/source.csv", "/tmp/target.csv")
	// Handle errors
	if err != nil {
		panic("Can't run remote command: " + err.Error())
	}
	fmt.Println("success")
}
