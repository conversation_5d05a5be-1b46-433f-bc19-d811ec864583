a na macos by to pos<PERSON><PERSON>?
<PERSON>k, jest bardzo duże prawdopodobieństwo, że testy jednostkowe (go test ./...) zadziałałyby poprawnie na systemie macOS.

Oto dlaczego:

1.  System operacyjny typu Unix: macOS, podobnie jak Linux, jest systemem z rodziny Unix. Oznacza to, że wiele podstawowych funkcji systemowych, w tym format komunikatów o błędach i struktura systemu plików, jest bardzo podobnych lub identycznych.
2.  Komunikat o błędzie: Komunikat o błędzie open abc: no such file or directory, którego oczekuje test TestGetKeyFile, jest standardowym komunikatem w systemach Unix/Linux, a więc również i w macOS. Błąd, który wystąpił w systemie Windows (The system cannot find the file specified.), jest specyficzny dla tego systemu.
3.  Ścieżka do pliku: Ścieżka /etc/ssh/ssh_host_rsa_key.pub, której szuka test TestRunCommandWithFingerprint, jest prawidłową ścieżką w systemach typu Unix. Nawet jeśli sam plik by nie istniał, system macOS zwróciłby błąd w formacie oczekiwanym przez test.

Co z make ssh-server?

Warto jednak zaznaczyć, że polecenie make ssh-server nadal by nie zadziałało na standardowym macOS. Używa ono poleceń specyficznych dla dystrybucji Linuksa Alpine (apk add, adduser), które nie są dostępne w macOS. Ta część jest ewidentnie przeznaczona do uruchamiania w kontenerze Docker w celu stworzenia środowiska do testów integracyjnych.

Podsumowując: Twoje problemy z uruchomieniem testów wynikają z tego, że zostały one napisane z myślą o środowisku uniksowym. Na macOS, które jest takim środowiskiem, te konkretne testy jednostkowe najprawdopodobniej by przeszły.




w testach uzywa tam spreparowanego klucza. jak to mozliwe ze ten klucz spasuje do tego noewgo servera ssh
To jest absolutnie kluczowe pytanie, które pokazuje, że rozumiesz sedno problemu! Masz rację, w testach używany jest spreparowany, istniejący wcześniej klucz.

Oto jak to działa, krok po kroku. To nie jest magia, a celowe działanie zdefiniowane w pliku Makefile.

W skrócie: Nowy serwer SSH jest instruowany, aby zaufać temu spreparowanemu kluczowi.

Nie jest tak, że klucz serwera pasuje do klucza klienta. Działa to w drugą stronę:
1.  Klient (test) ma parę kluczy: prywatny (sekretny) i publiczny (który można udostępniać).
2.  Serwer przechowuje listę kluczy publicznych, którym ufa. Ta lista znajduje się w pliku o nazwie authorized_keys.
3.  Jeśli klient próbuje się połączyć, serwer sprawdza, czy jego klucz publiczny znajduje się na liście authorized_keys. Jeśli tak, pozwala na połączenie.

Dowód w pliku Makefile

Spójrzmy ponownie na polecenie make ssh-server. Kluczowe są te linie:
makefile
Co tu się dzieje?
1.  mkdir -p /home/<USER>/.ssh: Tworzony jest katalog .ssh dla użytkownika drone-scp na nowym serwerze.
2.  cat tests/.ssh/id_rsa.pub >> /home/<USER>/.ssh/authorized_keys: To jest najważniejsza część. Polecenie to bierze zawartość pliku id_rsa.pub (klucz publiczny) z katalogu tests/.ssh w Twoim projekcie i dopisuje go do pliku authorized_keys na nowo tworzonym serwerze.

Dowód w plikach projektu

Pozwól, że pokażę Ci te spreparowane klucze, które leżą w Twoim projekcie i czekają na użycie: